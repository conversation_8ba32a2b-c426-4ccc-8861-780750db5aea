import React, { useEffect } from "react";

import { ChartContainer } from "./ChartContainer";
import { ChartHeader } from "./ChartHeader";

import type { ChartModalProps } from "../../types/chart";

export const ChartModal: React.FC<ChartModalProps> = ({
  data,
  onClose,
}) => {
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => { document.removeEventListener("keydown", handleEscape); };
  }, [onClose]);

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions
    <div
      className="chart-modal"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-label="Chart modal"
    >
      <div className="chart-modal-content">
        <ChartHeader
          symbol={data.symbol}
          conversionCurrency={data.conversionCurrency}
          onClose={onClose}
        />
        <ChartContainer data={data} />
      </div>
    </div>
  );
};
